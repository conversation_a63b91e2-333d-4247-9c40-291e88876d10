<?php 

use App\Models\Sale;
use App\Models\ProductLine;
use App\Models\Payment;

require(__DIR__."/../../php/init.php");

$sale_id = input("i");
$sale = Sale::find($sale_id);

if (!$sale) {
    _error_code(404);
}

$items = ProductLine::where("reltype", 'sales')->where("relid", $sale->id)->get();
$payments = Payment::where("reltype", 'sales')->where("relid", $sale->id)->get();

$page = [
    "title" => tr("Receipt") . " #" . $sale->id,
    "active" => "pos",
    "class" => "sidebar-xs",
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= tr("Receipt") ?> #<?= $sale->id ?></title>
    <link rel="stylesheet" href="<?= base_url('assets/bootstrap/css/bootstrap.min.css') ?>">
    <link rel="stylesheet" href="<?= base_url('assets/vendor/fontawesome/css/all.min.css') ?>">
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
        }
        
        .receipt-container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .receipt-header {
            background: #343a40;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .receipt-content {
            padding: 20px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px dashed #dee2e6;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .receipt-details {
            margin-bottom: 20px;
            border-bottom: 1px dashed #dee2e6;
            padding-bottom: 15px;
        }
        
        .receipt-items {
            margin-bottom: 20px;
        }
        
        .item-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .item-name {
            flex: 1;
            margin-right: 10px;
        }
        
        .item-qty-price {
            text-align: right;
            white-space: nowrap;
        }
        
        .item-total {
            text-align: right;
            font-weight: bold;
            min-width: 80px;
        }
        
        .receipt-totals {
            border-top: 2px dashed #dee2e6;
            padding-top: 15px;
            margin-bottom: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .total-row.grand-total {
            font-weight: bold;
            font-size: 16px;
            border-top: 1px solid #dee2e6;
            padding-top: 8px;
            margin-top: 8px;
        }
        
        .receipt-footer {
            text-align: center;
            border-top: 2px dashed #dee2e6;
            padding-top: 15px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .action-buttons {
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
 
        
      
        
        @media print {
            body {
                background: white;
            }
            
            .receipt-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
            
            .action-buttons {
                display: none;
            }
            
            .receipt-header {
                background: white !important;
                color: black !important;
            }
        }
        
        @media (max-width: 480px) {
            .receipt-container {
                margin: 10px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="receipt-header">
            <h4 class="mb-0"><?= tr("RECEIPT") ?></h4>
        </div>
        
        <div class="receipt-content">
            <!-- Company Information -->
            <div class="company-info">
                <div class="company-name"><?= get_option('company_name', 'StockV3') ?></div>
                <?php if (get_option('company_address')): ?>
                    <div class="small"><?= get_option('company_address') ?></div>
                <?php endif; ?>
                <?php if (get_option('company_phone')): ?>
                    <div class="small"><?= tr("Phone") ?>: <?= get_option('company_phone') ?></div>
                <?php endif; ?>
                <?php if (get_option('company_email')): ?>
                    <div class="small"><?= tr("Email") ?>: <?= get_option('company_email') ?></div>
                <?php endif; ?>
            </div>
            
            <!-- Receipt Details -->
            <div class="receipt-details">
                <div class="row">
                    <div class="col-6">
                        <strong><?= tr("Receipt") ?> #:</strong><br>
                        <span><?= $sale->id ?></span>
                    </div>
                    <div class="col-6 text-right">
                        <strong><?= tr("Date") ?>:</strong><br>
                        <span><?= date('Y-m-d H:i', strtotime($sale->created_at)) ?></span>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-6">
                        <strong><?= tr("Customer") ?>:</strong><br>
                        <span><?= $sale->customer ? $sale->customer->name : tr("Walk-in Customer") ?></span>
                    </div>
                    <div class="col-6 text-right">
                        <strong><?= tr("Cashier") ?>:</strong><br>
                        <span><?= auth()->name ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Items -->
            <div class="receipt-items">
                <?php foreach ($items as $item): ?>
                    <div class="item-row">
                        <div class="item-name">
                            <strong><?= $item->name ?></strong>
                            <?php if ($item->description): ?>
                                <br><small class="text-muted"><?= $item->description ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="item-row">
                        <div class="item-qty-price">
                            <?= _float($item->quantity) ?> x <?= _format_price($item->price, false) ?>
                            <?php if ($item->tax_percent > 0): ?>
                                <small class="text-muted">(+<?= $item->tax_percent ?>% <?= tr("tax") ?>)</small>
                            <?php endif; ?>
                        </div>
                        <div class="item-total">
                            <?= _format_price($item->total_price * $item->quantity) ?>
                        </div>
                    </div>
                    <hr class="my-2">
                <?php endforeach; ?>
            </div>
            
            <!-- Totals -->
            <div class="receipt-totals">
                <div class="total-row">
                    <span><?= tr("Subtotal") ?>:</span>
                    <span><?= _format_price($sale->total_price - $sale->total_tax) ?></span>
                </div>
                
                <?php if ($sale->total_tax > 0): ?>
                    <div class="total-row">
                        <span><?= tr("Tax") ?>:</span>
                        <span><?= _format_price($sale->total_tax) ?></span>
                    </div>
                <?php endif; ?>
                
                <?php if ($sale->discount > 0): ?>
                    <div class="total-row">
                        <span><?= tr("Discount") ?>:</span>
                        <span>-<?= _format_price($sale->discount) ?></span>
                    </div>
                <?php endif; ?>
                
                <div class="total-row grand-total">
                    <span><?= tr("TOTAL") ?>:</span>
                    <span><?= _format_price($sale->total_price) ?></span>
                </div>
                
                <div class="total-row">
                    <span><?= tr("PAID") ?>:</span>
                    <span><?= _format_price($sale->paid) ?></span>
                </div>
                
                <?php 
                $change = $sale->paid - $sale->total_price;
                if ($change > 0): 
                ?>
                    <div class="total-row">
                        <span><?= tr("CHANGE") ?>:</span>
                        <span><?= _format_price($change) ?></span>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Payment Methods -->
            <?php if ($payments->count() > 0): ?>
                <div class="receipt-details">
                    <strong><?= tr("Payment Methods") ?>:</strong><br>
                    <?php foreach ($payments as $payment): ?>
                        <div class="d-flex justify-content-between">
                            <span><?= Payment::getPaymentMethods()[$payment->method] ?? $payment->method ?></span>
                            <span><?= _format_price($payment->amount) ?></span>
                        </div>
                        <?php if ($payment->reference): ?>
                            <small class="text-muted"><?= tr("Ref") ?>: <?= $payment->reference ?></small><br>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- Footer -->
            <div class="receipt-footer">
                <div><?= tr("Thank you for your purchase!") ?></div>
                <?php if (get_option('receipt_footer')): ?>
                    <div class="mt-2"><?= get_option('receipt_footer') ?></div>
                <?php endif; ?>
                <div class="mt-2">
                    <small><?= tr("Powered by StockV3") ?></small>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button type="button" class="btn btn-success btn-print" onclick="window.print()">
                <i class="fa fa-print mr-2"></i><?= tr("Print Receipt") ?>
            </button>
            <button type="button" class="btn btn-secondary btn-back" onclick="goBack()">
                <i class="fa fa-arrow-left mr-2"></i><?= tr("Back to POS") ?>
            </button>
        </div>
    </div>

    <script>
        function goBack() {
            // Try to go back to the previous page, or close the tab if opened in new tab
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // If this was opened in a new tab, close it
                window.close();
                // If window.close() doesn't work (some browsers block it), redirect to POS
                setTimeout(function() {
                    window.location.href = '<?= base_url("sales/pos.php") ?>';
                }, 100);
            }
        }
        
        // Auto-focus on print button for keyboard accessibility
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.btn-print').focus();
        });
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P or Cmd+P for print
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape key to go back
            if (e.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html> 